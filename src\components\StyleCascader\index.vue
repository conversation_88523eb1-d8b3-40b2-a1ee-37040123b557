<template>
  <div style="width: 300px">
    <n-select
      clearable
      ref="selectRef"
      v-model:value="checkedValue"
      placeholder="请选择技术规范"
      :options="options"
      :consistent-menu-width="false"
    />
  </div>
</template>

<script lang="ts" setup>
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { onMounted, ref, watch, nextTick } from 'vue';
  const props = defineProps(['onlyProviders', 'disabledCheckedAll', 'modelValue']);
  const emit = defineEmits(['update:modelValue']);
  const checkedValue = ref('');
  const options = ref<any[]>([]);
  const userStore = useUserStore();
  const { providers } = storeToRefs(userStore);
  const selectRef = ref<any>(null);
  
  // 监听外部传入的modelValue变化
  watch(() => props.modelValue, (newValue) => {
    console.log('StyleCascader modelValue changed:', newValue);
    checkedValue.value = newValue || '';
  }, { immediate: true });
  
  watch(checkedValue, (value) => {
    console.log('StyleCascader checkedValue changed:', value);
    // 确保传出的值总是字符串类型
    emit('update:modelValue', value ? String(value) : '');
  });

  // 更新选项的函数
  const updateOptions = () => {
    console.log('StyleCascader updateOptions, providers:', providers.value);
    if (providers.value && providers.value.length > 0) {
      options.value = [
        ...providers.value.map((i) => ({
          value: String(i.id), // 确保value是字符串
          label: i.title,
        })),
      ];
      if (!props.disabledCheckedAll) {
        options.value.unshift({
          value: '',
          label: '全部',
        });
      }
      console.log('StyleCascader options updated:', options.value);
    } else {
      options.value = [];
    }
  };

  // 监听providers数据变化，更新选项
  watch(() => providers.value, (newProviders) => {
    console.log('StyleCascader providers changed:', newProviders);
    if (newProviders && newProviders.length > 0) {
      updateOptions();
      // 如果当前有选中值，确保在选项更新后重新设置
      if (checkedValue.value) {
        nextTick(() => {
          console.log('StyleCascader re-setting checkedValue:', checkedValue.value);
          checkedValue.value = String(checkedValue.value);
        });
      }
    } else {
      options.value = [];
    }
  }, { immediate: true });

  onMounted(() => {
    console.log('StyleCascader mounted, providers:', providers.value);
    // 初始化时如果providers已有数据，直接设置选项
    if (providers.value && providers.value.length > 0) {
      updateOptions();
    }
  });
</script>
