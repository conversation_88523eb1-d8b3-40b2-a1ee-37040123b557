<template>
  <div class="material-audit-container">
    <!-- 头部信息 -->
    <div class="audit-header">
      <div class="audit-title">
        <h2>{{ auditTitle }}</h2>
      </div>
      <div class="audit-actions">
        <el-button type="danger" @click="showRejectDialog">驳回</el-button>
        <el-popconfirm title="确定审核通过吗？" @confirm="handleApprove" width="200px">
          <template #reference>
            <el-button type="primary">通过</el-button>
          </template>
        </el-popconfirm>
        <el-button @click="goBack" plain>
            返回
        </el-button>
      </div>
    </div>

    <!-- 审核信息展示 -->
    <div class="audit-info" v-if="auditData">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="资源名称">{{ auditData.name }}</el-descriptions-item>
        <el-descriptions-item label="归属合作伙伴">{{ auditData.provider?.title }}</el-descriptions-item>
        <el-descriptions-item label="CP制作者">{{ auditData.user?.name }}</el-descriptions-item>
        <el-descriptions-item label="提交时间">{{ formatDate(auditData.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="素材类型">{{ materialTypeName }}</el-descriptions-item>
        <el-descriptions-item label="文件格式">{{ getFileFormat(auditData.url) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2" v-if="auditData.description">
          {{ auditData.description }}
        </el-descriptions-item>
        <el-descriptions-item label="驳回原因" :span="2" v-if="auditData.status === 4 && auditData.revising_reason">
          <el-text type="danger">{{ auditData.revising_reason }}</el-text>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-message">
      <el-alert
        title="加载失败"
        type="error"
        :description="errorMessage"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 3D模型预览容器 -->
    <div class="model-viewer-container" v-if="!error && modelUrl">
      <ModelViewer
        ref="modelViewerRef"
        :modelUrl="modelUrl"
        :backgroundColor="backgroundColor"
        :autoRotate="autoRotate"
        @camera-changed="handleCameraChanged"
      />

      <!-- 3D控制按钮 -->
      <div class="model-controls">
        <el-button-group>
          <el-button
            :type="canReset ? 'primary' : 'default'"
            :disabled="!canReset"
            @click="resetCamera"
          >
            重置视角
          </el-button>
          <el-button type="primary" @click="toggleAutoRotate">
            {{ autoRotate ? '停止旋转' : '开始旋转' }}
          </el-button>
          <el-button-group class="background-toggle">
            <el-button
              :type="backgroundColor === '#ffffff' ? 'primary' : 'default'"
              @click="setBackground('#ffffff')">
              亮色背景
            </el-button>
            <el-button
              :type="backgroundColor === '#2a2a2a' ? 'primary' : 'default'"
              @click="setBackground('#2a2a2a')">
              暗色背景
            </el-button>
          </el-button-group>
        </el-button-group>
      </div>
    </div>

    <!-- 无模型时的占位 -->
    <div v-if="!error && !modelUrl" class="no-model-placeholder">
      <el-empty description="该素材暂无3D模型预览" />
    </div>

    <!-- 审核操作按钮 -->
<!--     <div class="audit-footer">
      <el-button @click="goBack">返回</el-button>
      <el-button type="danger" @click="showRejectDialog">驳回</el-button>
      <el-popconfirm title="确定审核通过吗？" @confirm="handleApprove" width="200px">
        <template #reference>
          <el-button type="primary">通过</el-button>
        </template>
      </el-popconfirm>
    </div> -->

    <!-- 驳回对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="审核意见" width="40%" append-to-body>
      <el-form ref="rejectFormRef" :model="rejectForm" :rules="rejectRules" label-width="95px">
        <el-form-item label="审核意见：" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            placeholder="请描述驳回原因"
            maxlength="200"
            rows="6"
            style="width: 100%"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleReject" :loading="submitting">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import moment from 'moment';
import ModelViewer from '@/components/Online3DViewer/ModelViewer.vue';
import { getAuditAssetDetail, auditAssetPublish } from '@/api/material/material';

const route = useRoute();
const router = useRouter();

// 3D模型相关
const modelViewerRef = ref<any>(null);
const modelUrl = ref('');
const autoRotate = ref(true);
const backgroundColor = ref('#ffffff');
const error = ref(false);
const errorMessage = ref('');
const canReset = ref(false); // 是否可以重置视角

// 审核数据
const auditData = ref<any>(null);
const submitting = ref(false);

// 驳回对话框
const rejectDialogVisible = ref(false);
const rejectFormRef = ref<FormInstance>();
const rejectForm = ref({
  reason: ''
});

const rejectRules: FormRules = {
  reason: [
    { required: true, message: '请输入驳回原因', trigger: 'blur' },
    { min: 5, message: '驳回原因至少5个字符', trigger: 'blur' }
  ]
};

// 计算属性
const auditTitle = computed(() => {
  return auditData.value?.name ? `素材审核` : '素材审核';
});

const materialTypeName = computed(() => {
  return route.query.name as string || '未知类型';
});

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '';
  return moment(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取文件格式
const getFileFormat = (url: string) => {
  if (!url) return '未知';
  const extension = url.split('.').pop()?.toLowerCase();
  return extension === 'bundle' ? 'FBX (Bundle)' : extension?.toUpperCase() || '未知';
};

// 3D控制方法
const setBackground = (color: string) => {
  backgroundColor.value = color;
};

// 处理相机状态变化
const handleCameraChanged = (hasChanged: boolean) => {
  canReset.value = hasChanged;
};

const resetCamera = () => {
  if (modelViewerRef.value) {
    modelViewerRef.value.resetCamera();
  }
};

const toggleAutoRotate = () => {
  autoRotate.value = !autoRotate.value;
};

// 模型加载事件处理（ModelViewer组件内部处理加载状态）

// 审核操作
const showRejectDialog = () => {
  rejectDialogVisible.value = true;
  rejectForm.value.reason = '';
};

const handleApprove = async () => {
  try {
    submitting.value = true;
    const bundleId = route.query.bundleId as string;
    
    await auditAssetPublish(bundleId, {
      audit_result: '1'
    });
    
    ElMessage.success('审核通过成功！');
    goBack();
  } catch (err) {
    console.error('审核通过失败:', err);
    ElMessage.error('审核通过失败，请重试');
  } finally {
    submitting.value = false;
  }
};

const handleReject = async () => {
  if (!rejectFormRef.value) return;
  
  try {
    await rejectFormRef.value.validate();
    submitting.value = true;
    
    const bundleId = route.query.bundleId as string;
    
    await auditAssetPublish(bundleId, {
      audit_result: '0',
      audit_reason: rejectForm.value.reason
    });
    
    ElMessage.success('审核驳回成功！');
    rejectDialogVisible.value = false;
    goBack();
  } catch (err) {
    console.error('审核驳回失败:', err);
    ElMessage.error('审核驳回失败，请重试');
  } finally {
    submitting.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.push({ name: 'material-list' });
};

// 加载审核数据
const loadAuditData = async () => {
  try {
    const bundleId = route.query.bundleId as string;
    if (!bundleId) {
      throw new Error('缺少素材ID');
    }
    
    const res = await getAuditAssetDetail(bundleId);
    if (res && res.data) {
      auditData.value = res.data;
      
      // 设置模型URL
      if (res.data.url) {
        modelUrl.value = res.data.url;
      } else if (route.query.bundlePath) {
        modelUrl.value = route.query.bundlePath as string;
      }
    }
  } catch (err) {
    console.error('加载审核数据失败:', err);
    ElMessage.error('加载审核数据失败');
    error.value = true;
    errorMessage.value = '加载审核数据失败';
  }
};

onMounted(async () => {
  await loadAuditData();

  // 如果没有模型URL，不显示错误，只是不显示3D预览
  // ModelViewer组件会自己处理加载状态
});
</script>

<style scoped>
.material-audit-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  background-color: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
}

.audit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.audit-title h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #303133;
}

.audit-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.audit-info {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.model-viewer-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 400px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.model-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  gap: 10px;
}

.background-toggle {
  margin-left: 10px;
}

.no-model-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  min-height: 400px;
}

.audit-footer {
  background: #fff;
  text-align: center;
  padding: 15px 0;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* 加载状态由ModelViewer组件内部处理 */

.error-message {
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .audit-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .audit-actions {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .model-controls {
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 5px;
  }

  .audit-footer {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
