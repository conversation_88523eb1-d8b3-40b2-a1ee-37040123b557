<template>
  <div class="model3d-preview-container">
    <div class="model-header">
      <div class="model-title">
        <!-- <h2>{{ modelName || '资源预览' }}</h2> -->
        <h2>{{'素材预览' }}</h2>
      </div>
      <div class="model-actions">
        <el-button @click="showWatermarkDialog" type="success" :loading="watermarkDownloading">
          <el-icon><Download /></el-icon>
          {{ watermarkDownloading ? '加密下载中...' : '水印加密下载' }}
        </el-button>
        <el-button @click="downloadModel" type="primary" :loading="downloading">
          <el-icon><Download /></el-icon>
          {{ downloading ? '下载中...' : '资源下载' }}
        </el-button>
        <el-button @click="goBack" plain>
          返回
        </el-button>
      </div>
    </div>

    <div class="model-info" v-if="modelInfo">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="资源名称">{{ modelInfo.name }}</el-descriptions-item>
        <el-descriptions-item label="文件格式">{{ modelInfo.format }}</el-descriptions-item>
        <el-descriptions-item label="文件大小">
          <span v-if="fileSizeLoading">获取中...</span>
          <span v-else>{{ formatFileSize(actualFileSize || modelInfo.size) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="上传时间">{{ formatDate(modelInfo.uploadTime) }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="error" class="error-message">
      <el-alert
        title="加载失败"
        type="error"
        description="模型加载失败，请确保链接正确且模型格式受支持。"
        show-icon
        :closable="false"
      />
    </div>
    
    <div class="model-viewer-container">
      <ModelViewer
        ref="modelViewerRef"
        :modelUrl="modelUrl"
        :backgroundColor="backgroundColor"
        :autoRotate="autoRotate"
        @camera-changed="handleCameraChanged"
      />
      
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载模型，请稍候...</div>
      </div>
      
      <div class="model-controls">
        <el-button-group>
          <el-button
            :type="canReset ? 'primary' : 'default'"
            :disabled="!canReset"
            @click="resetCamera"
          >
            重置视角
          </el-button>
          <el-button type="primary" @click="toggleAutoRotate">
            {{ autoRotate ? '停止旋转' : '开始旋转' }}
          </el-button>
          <el-button-group class="background-toggle">
            <el-button 
              :type="backgroundColor === '#ffffff' ? 'primary' : 'default'" 
              @click="setBackground('#ffffff')">
              亮色背景
            </el-button>
            <el-button 
              :type="backgroundColor === '#2a2a2a' ? 'primary' : 'default'"
              @click="setBackground('#2a2a2a')">
              暗色背景
            </el-button>
          </el-button-group>
        </el-button-group>
      </div>
    </div>
  </div>

  <!-- 水印加密下载弹窗 -->
  <el-dialog
    v-model="watermarkDialogVisible"
    title="水印加密下载"
    width="500px"
    height="500px"
    append-to-body
  >
    <el-form :model="watermarkForm" label-width="80px">
      <el-form-item label="水印内容">
        <el-input
          v-model="watermarkForm.content"
          type="textarea"
          placeholder="请输入水印内容"
          :rows="3"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelWatermarkDialog">取消</el-button>
        <el-button
          type="primary"
          @click="confirmWatermarkDownload"
          :loading="watermarkDownloading"
        >
          {{ watermarkDownloading ? '处理中...' : '添加' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ModelViewer from './ModelViewer.vue';
import moment from 'moment';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';

const route = useRoute();
const router = useRouter();
const modelViewerRef = ref<any>(null);
const modelUrl = ref('');
const autoRotate = ref(true);
const backgroundColor = ref('#ffffff');
const modelInfo = ref<{
  name: string;
  format: string;
  size: number;
  uploadTime: string;
} | null>(null);
const loading = ref(false);
const error = ref(false);
const errorMessage = ref('');
const source = ref(''); // 来源标识：avatar或material
const downloading = ref(false); // 下载状态
const actualFileSize = ref(0); // 实际文件大小
const fileSizeLoading = ref(false); // 文件大小加载状态
const canReset = ref(false); // 是否可以重置视角
const loadingProgress = ref(0); // 模型加载进度

// 水印加密下载相关状态
const watermarkDialogVisible = ref(false); // 水印弹窗显示状态
const watermarkDownloading = ref(false); // 水印下载状态
const watermarkForm = ref({
  content: '' // 水印内容
});

// 模型名称
const modelName = computed(() => {
  // 根据来源显示不同的标题前缀
  const prefix = source.value === 'avatar' ? '形象' : '素材';
  return modelInfo.value?.name ? `${prefix}：${modelInfo.value.name}` : `${prefix}预览`;
});

// 设置背景颜色
const setBackground = (color: string) => {
  backgroundColor.value = color;
};

// 处理相机状态变化
const handleCameraChanged = (hasChanged: boolean) => {
  canReset.value = hasChanged;
};

// 重置相机
const resetCamera = () => {
  if (modelViewerRef.value) {
    modelViewerRef.value.resetCamera();
  }
};

// 切换自动旋转
const toggleAutoRotate = () => {
  autoRotate.value = !autoRotate.value;
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (!bytes || bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '';
  return moment(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取文件实际大小
const getFileSize = async (url: string) => {
  try {
    // 首先尝试使用 HEAD 请求获取文件大小
    const response = await fetch(url, {
      method: 'HEAD',
      mode: 'cors'
    });

    if (response.ok) {
      const contentLength = response.headers.get('content-length');
      if (contentLength) {
        const size = parseInt(contentLength, 10);
        if (size > 0) {
          return size;
        }
      }
    }

    // 如果 HEAD 请求失败，尝试 GET 请求但只读取部分内容
    console.warn('HEAD 请求失败，尝试 GET 请求获取文件大小');
    const getResponse = await fetch(url, {
      method: 'GET',
      mode: 'cors'
    });

    if (getResponse.ok) {
      const contentLength = getResponse.headers.get('content-length');
      if (contentLength) {
        const size = parseInt(contentLength, 10);
        if (size > 0) {
          // 取消请求以节省带宽
          getResponse.body?.cancel?.();
          return size;
        }
      }
    }
  } catch (error) {
    console.warn('无法获取文件大小:', error);
  }

  return 0;
};

// 下载模型资源
const downloadModel = async () => {
  if (!modelUrl.value) {
    ElMessage.error('模型URL不存在，无法下载');
    return;
  }

  downloading.value = true;

  try {
    // 设置下载文件名
    const fileName = modelInfo.value?.name || 'model';
    const fileExtension = modelInfo.value?.format?.toLowerCase() || 'bundle';
    const fullFileName = `${fileName}.${fileExtension}`;

    // 尝试使用fetch下载文件（支持跨域和进度监控）
    const response = await fetch(modelUrl.value);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 获取文件内容
    const blob = await response.blob();

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fullFileName;

    // 添加到DOM并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    ElMessage.success(`模型资源 "${fullFileName}" 下载成功`);
  } catch (error) {
    console.error('下载失败:', error);

    // 如果fetch失败，尝试直接链接下载
    try {
      const link = document.createElement('a');
      link.href = modelUrl.value;
      link.download = `${modelInfo.value?.name || 'model'}.${modelInfo.value?.format?.toLowerCase() || 'bundle'}`;
      link.target = '_blank';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      ElMessage.success('模型资源下载成功');
    } catch (fallbackError) {
      console.error('备用下载方法也失败:', fallbackError);
      ElMessage.error('下载失败，请检查网络连接或联系管理员。');
    }
  } finally {
    downloading.value = false;
  }
};

// 水印加密下载相关函数
// 显示水印弹窗
const showWatermarkDialog = () => {
  watermarkDialogVisible.value = true;
  watermarkForm.value.content = ''; // 重置水印内容
};

// 取消水印弹窗
const cancelWatermarkDialog = () => {
  watermarkDialogVisible.value = false;
  watermarkForm.value.content = '';
};

// 确认水印下载
const confirmWatermarkDownload = async () => {
  if (!watermarkForm.value.content.trim()) {
    ElMessage.warning('请输入水印内容');
    return;
  }

  if (!modelUrl.value) {
    ElMessage.error('模型URL不存在，无法下载');
    return;
  }

  watermarkDownloading.value = true;

  try {
    // TODO: 这里需要调用后端API进行水印加密处理
    // 目前先模拟处理过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    ElMessage.success(`已添加水印"${watermarkForm.value.content}"并开始下载`);

    // 关闭弹窗
    watermarkDialogVisible.value = false;
    watermarkForm.value.content = '';

    // TODO: 实际的水印加密下载逻辑
    // 这里应该调用后端API，传递水印内容和模型URL
    // 后端处理后返回加密的文件供下载

  } catch (error) {
    console.error('水印加密下载失败:', error);
    ElMessage.error('水印加密下载失败，请重试');
  } finally {
    watermarkDownloading.value = false;
  }
};

// 返回上一页
const goBack = () => {
  // 根据来源决定返回的路由
  if (source.value === 'avatar') {
    router.push({ name: 'avatar-list' });
  } else if (source.value === 'material') {
    router.push({ name: 'material-list' });
  } else {
    router.go(-1);
  }
};

onMounted(() => {
  loading.value = true;
  
  // 获取来源参数
  if (route.query.source) {
    source.value = route.query.source as string;
    console.log("Source:", source.value);
  }
  
  // 从URL参数中获取模型URL和信息
  if (route.query.modelUrl) {
    modelUrl.value = route.query.modelUrl as string;
    console.log("Model URL:", modelUrl.value);
  } else {
    error.value = true;
    errorMessage.value = '模型URL不存在';
    ElMessage.error('模型URL不存在');
    loading.value = false;
  }
  
  // 如果有模型信息，则解析它
  if (route.query.modelInfo) {
    try {
      modelInfo.value = JSON.parse(route.query.modelInfo as string);
      console.log("Model Info:", modelInfo.value);
    } catch (error) {
      console.error('解析模型信息失败:', error);
      ElMessage.warning('解析模型信息失败');
    }
  }

  // 获取实际文件大小
  if (modelUrl.value) {
    fileSizeLoading.value = true;
    getFileSize(modelUrl.value).then(size => {
      actualFileSize.value = size;
      console.log("Actual file size:", actualFileSize.value);
    }).catch(error => {
      console.warn('获取文件大小失败:', error);
    }).finally(() => {
      fileSizeLoading.value = false;
    });
  }

  loading.value = false;
});
</script>

<style scoped>
.model3d-preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.model-title h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #303133;
}

.model-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.model-info {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.model-viewer-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 500px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.model-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  gap: 10px;
}

.background-toggle {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .model-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .model-actions {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .model-controls {
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 5px;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #333;
}

.error-message {
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 水印弹窗样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 