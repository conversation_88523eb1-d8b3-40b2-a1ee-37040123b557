<template>
    <el-card class="n-layout-page-header">
      <el-form :model="form" ref="formRef" label-width="80px" :rules="rules">
        <el-form-item label="资源名称" required prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入资源名称"
            maxlength="100"
            @input="(value) => handleInput('name', value)"
          />
        </el-form-item>
        <el-form-item label="素材类型" required prop="type">
          <el-select
            v-model="form.type"
            popper-class="material-type-select-dropdown"
            :teleported="true"
            style="width: 100%"
            @visible-change="handleSelectVisibleChange"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.name"
              :label="item.chinese"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <!-- 风格类型隐藏，使用默认值 -->
        <el-form-item label="风格类型" required prop="style_id" style="display: none;">
          <el-select v-model="form.style_id" disabled>
            <el-option
              v-for="item in styles"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="适用范围" required prop="gender">
          <el-select
            v-model="form.gender"
            popper-class="gender-select-dropdown"
            :teleported="true"
            style="width: 100%"
            @visible-change="handleGenderSelectVisibleChange"
          >
            <el-option
              label="男性"
              value="male"
            />
            <el-option
              label="女性"
              value="female"
            />
            <el-option
              label="通用"
              value="all"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="缩略图" required prop="icon_id">
          <CustomUpload
            v-model="iconInfo"
            scope="asset_icon"
            :show-file-list="false"
            list-type="picture-card"
            accept=".jpeg,.png,.jpg"
            :maxSize="4"
          >
            <template v-if="iconInfo.url">
                <img alt="图片" :src="iconInfo.url" class="avatar">
            </template>
            <template v-else>
              <el-icon size="26"><Plus /></el-icon>
              <div class="el-upload__text">
                点击或拖拽文件上传
              </div>
            </template>
            <template #tip>
                <div class="el-upload__tip">
                建议尺寸：640*320，小于4M的jpg、jpeg、png格式
                </div>
            </template>
          </CustomUpload>
        </el-form-item>
        <el-form-item label="资源文件" required prop="file_id">
          <CustomUpload
            v-model="fileInfo"
            scope="asset"
            :showFileList="false"
            accept=".fbx,.glb,.obj,.gltf,.stl,.dae,.ply"
            :maxSize="50"
            @upload="(v) => {
              fileUploading = v
            }"
          >
          <div style="display: flex;flex-direction: column;align-items: center;" v-loading="fileUploading">
              <template v-if="fileInfo.url" >
                <img alt="图片" src="../../../assets/images/Bundle.png" class="avatar" style="width: 100px;height: 100px;">
                {{fileInfo.name}}
              </template>
              <template v-else>
                <el-icon size="26"><Plus /></el-icon>
                <div class="el-upload__text">
                  点击或拖拽文件上传（.fbx、.glb、.obj、.gltf、.stl、.dae、.ply文件）
                </div>
              </template>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                小于50M的.fbx、.glb、.obj、.gltf、.stl、.dae、.ply文件，最多上传1个
              </div>
            </template>
          </CustomUpload>
        </el-form-item>
        <el-form-item label="备注" required prop="description">
          <el-input
            v-model="form.description" 
            type="textarea"
            rows="6"
            maxlength="100"
            @input="(value) => handleInput('description', value)"
          />
        </el-form-item>
      </el-form>
      <el-button @click="submit">提交</el-button>
      <el-button @click="goback">返回</el-button>
    </el-card>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus'
  import type {FormInstance} from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'
  import {addAsset, getAssetTypeList, getAssetDetail, editAsset} from '@/api/material/material'
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import CustomUpload from '@/views/operator/components/CustomUpload.vue';
import { useFilterInputHander } from '@/views/hooks';

  const router = useRouter()
  const route = useRoute();
  const userStore = useUserStore();
  const { styles, defaultStyle, currentProviderId } = storeToRefs(userStore);
  const fileUploading = ref(false)
  const formRef = ref<FormInstance>()
  const form = reactive({
      name: '',
      type: '',
      style_id: '', // 初始化为空，在 onMounted 中设置
      gender: '',
      description: '',
      provider_id: '', // 初始化为空，在 onMounted 中设置
      icon_id: '',
      file_id: '',
  })
  const handleInput = useFilterInputHander(form);

  const rules = {
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
    ],
    type: [
      { required: true, message: '请选择类型', trigger: 'change' },
    ],
    style_id: [
      { required: true, message: '请选择风格类型', trigger: 'blur' },
    ],
    gender: [
      { required: true, message: '请选择适用范围', trigger: 'blur' },
    ],
    description: [
      { required: true, message: '请输入描述', trigger: 'blur' },
    ],
    icon_id: [
      {
        validator: (_, __, callback) => {
          if (!iconInfo.value.id) {
            callback(new Error('请上传图标'))
          } else {
            callback()
          }
        }
      }
    ],
    file_id: [
      {
        validator: (_, __, callback) => {
          if (!fileInfo.value.id) {
            callback(new Error('请上传资源文件'))
          } else {
            callback()
          }
        }
      }
    ]
  }

  const iconInfo = ref<any>({})
  const fileInfo = ref<any>({})

  const {id} = route.query
  onMounted(async () => {
    try {
      await queryMaterialType()

      // 确保风格类型和提供商ID有默认值
      if (!form.style_id) {
        const stylesArray = styles.value || [];
        const defaultStyleValue = defaultStyle.value;

        if (defaultStyleValue !== undefined) {
          form.style_id = String(defaultStyleValue);
        } else if (stylesArray.length > 0) {
          form.style_id = String(stylesArray[0]?.id || '');
        }
      }

      if (!form.provider_id && currentProviderId.value) {
        form.provider_id = String(currentProviderId.value);
      }

      if (id) {
        const res = await getAssetDetail(id)
        for (let k in form) {
          form[k] = res.data[k]
        }
        iconInfo.value = {
          url: res.data.url_icon,
          id: res.data.icon_id,
        }
        fileInfo.value = {
          name: res.data.file.originalname,
          url: res.data.url,
          id: res.data.file.id
        }
      }
    } catch (error) {
      console.error('初始化页面时出错:', error);
    }
  })

  const typeOptions = ref<any[]>([])
  async function queryMaterialType() {
    try {
      const res = await getAssetTypeList()
      if (res && res.data && Array.isArray(res.data)) {
        typeOptions.value = res.data.reduce((prev, item) => {
          return [...prev, ...(item.children || [])]
        }, [])
      }
    } catch (error) {
      console.error('获取素材类型失败:', error);
      typeOptions.value = [];
    }
  }
  watch(iconInfo, () => {
    form.icon_id = iconInfo.value.id
    formRef.value?.validateField('icon_id')
  }, {deep: true})
  watch(fileInfo, () => {
    form.file_id = fileInfo.value.id
    formRef.value?.validateField('file_id')
    if (fileInfo.value.gender) {
      form.gender = fileInfo.value.gender
    }
    // 移除自动风格选择逻辑，因为模型不再包含风格文件
    // 保持使用默认风格值
  }, {deep: true})
  async function submit() {
    if (!form.style_id) {
      const stylesArray = styles.value || [];
      if (stylesArray.length > 0) {
        form.style_id = String(stylesArray[0].id);
      } else {
        form.style_id = '1';
      }
    }
    console.log(form.style_id)
    if (!formRef.value) return
    formRef.value.validate(async (valid) => {
      if (valid) {
        if (id) {
          await editAsset(
            id,
            {
              ...form,
            }
          )
          ElMessage({
            message: '编辑成功！',
            type: 'success'
          })
        } else {
          await addAsset({
            ...form,
          })
          ElMessage({
            message: '新增成功！',
            type: 'success'
          })
        }
        setTimeout(() => {
          goback()
        }, 1000)
      } else {
        console.log('表单数据：', form)
        ElMessage.error('请检查所有必填项，确保填写完整！')
      }
    })
  }

  function goback() {
    router.push({ path: '/material/material-list'});
  }

  // 处理素材类型下拉框显示状态变化，动态调整宽度
  const handleSelectVisibleChange = (visible: boolean) => {
    if (visible) {
      // 延迟执行，确保DOM已渲染
      setTimeout(() => {
        const dropdown = document.querySelector('.material-type-select-dropdown');
        if (dropdown) {
          (dropdown as HTMLElement).style.width = '300px';
          (dropdown as HTMLElement).style.maxWidth = '300px';

          // 调整选项宽度
          const items = dropdown.querySelectorAll('.el-select-dropdown__item');
          items.forEach(item => {
            (item as HTMLElement).style.maxWidth = '280px';
            (item as HTMLElement).style.width = 'auto';
          });
        }
      }, 10);
    }
  };

  // 处理适用范围下拉框显示状态变化，动态调整宽度
  const handleGenderSelectVisibleChange = (visible: boolean) => {
    if (visible) {
      // 延迟执行，确保DOM已渲染
      setTimeout(() => {
        const dropdown = document.querySelector('.gender-select-dropdown');
        if (dropdown) {
          (dropdown as HTMLElement).style.width = '200px';
          (dropdown as HTMLElement).style.maxWidth = '200px';

          // 调整选项宽度
          const items = dropdown.querySelectorAll('.el-select-dropdown__item');
          items.forEach(item => {
            (item as HTMLElement).style.maxWidth = '180px';
            (item as HTMLElement).style.width = 'auto';
          });
        }
      }, 10);
    }
  };

</script>

<style lang="less" scoped>
  .upload-demo {
    :deep(.el-upload--picture-card) {
      --el-upload-picture-card-size: unset;
      .el-upload-dragger {
        height: 100%;
        .avatar {
          max-width: 250px;
          max-height: 250px;
        }
      }
    }
    :deep(.el-upload-list--picture-card) {
      min-height: 156px;
      .el-upload--picture-card:nth-child(2) {
        display: none;
      }
    }
  }

  // 修复素材类型下拉框样式问题
  :deep(.el-select) {
    width: 100%;
  }
</style>

<style lang="less">
  // 全局样式，修复下拉框滚动条和悬停样式
  .material-type-select-dropdown {
    max-height: 300px !important;
    min-width: 200px !important;
    max-width: 300px !important;
    width: auto !important;
    z-index: 9999 !important;

    .el-select-dropdown__wrap {
      max-height: 300px !important;
    }

    .el-select-dropdown__item {
      max-width: 280px !important;
      width: auto !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      padding: 0 20px !important;
      line-height: 34px !important;
      height: 34px !important;
      box-sizing: border-box !important;

      &:hover {
        background-color: #f5f7fa !important;
        max-width: 280px !important;
        width: auto !important;
      }

      &.selected {
        background-color: #e6f7ff !important;
        color: #1890ff !important;
        max-width: 280px !important;
      }
    }

    .el-scrollbar {
      max-width: 300px !important;

      .el-scrollbar__wrap {
        max-width: 300px !important;
      }

      .el-scrollbar__bar {
        right: 2px !important;
        z-index: 1 !important;

        &.is-vertical {
          width: 6px !important;
          top: 2px !important;
          bottom: 2px !important;

          .el-scrollbar__thumb {
            background-color: rgba(144, 147, 153, 0.3) !important;
            border-radius: 3px !important;
            width: 6px !important;

            &:hover {
              background-color: rgba(144, 147, 153, 0.5) !important;
            }
          }
        }
      }

      .el-scrollbar__view {
        padding: 6px 0 !important;
        max-width: 300px !important;
      }
    }
  }

  // 适用范围下拉框样式
  .gender-select-dropdown {
    max-height: 200px !important;
    min-width: 120px !important;
    max-width: 200px !important;
    width: auto !important;
    z-index: 9999 !important;

    .el-select-dropdown__wrap {
      max-height: 200px !important;
    }

    .el-select-dropdown__item {
      max-width: 180px !important;
      width: auto !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      padding: 0 20px !important;
      line-height: 34px !important;
      height: 34px !important;
      box-sizing: border-box !important;

      &:hover {
        background-color: #f5f7fa !important;
        max-width: 180px !important;
        width: auto !important;
      }

      &.selected {
        background-color: #e6f7ff !important;
        color: #1890ff !important;
        max-width: 180px !important;
      }
    }

    .el-scrollbar {
      max-width: 200px !important;

      .el-scrollbar__wrap {
        max-width: 200px !important;
      }

      .el-scrollbar__bar {
        right: 2px !important;
        z-index: 1 !important;

        &.is-vertical {
          width: 6px !important;
          top: 2px !important;
          bottom: 2px !important;

          .el-scrollbar__thumb {
            background-color: rgba(144, 147, 153, 0.3) !important;
            border-radius: 3px !important;
            width: 6px !important;

            &:hover {
              background-color: rgba(144, 147, 153, 0.5) !important;
            }
          }
        }
      }

      .el-scrollbar__view {
        padding: 6px 0 !important;
        max-width: 200px !important;
      }
    }
  }
</style>
