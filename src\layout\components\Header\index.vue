<template>
  <div class="layout-header">
    <div
      class="layout-header-left"
      v-if="navMode === 'horizontal' || (navMode === 'horizontal-mix' && mixMenu)"
    >
      <div class="logo" v-if="navMode === 'horizontal'">
        <img alt="图片" :src="websiteConfig.logo" />
        <h2 v-show="!collapsed" class="title">{{ websiteConfig.title }}</h2>
      </div>
      <AsideMenu
        v-model:collapsed="collapsed"
        v-model:location="getMenuLocation"
        :inverted="getInverted"
        mode="horizontal"
      />
    </div>
    <!--左侧菜单-->
    <div class="layout-header-left" v-else>
      <!-- 菜单收起 -->
      <div
        class="ml-1 layout-header-trigger layout-header-trigger-min"
        @click="() => $emit('update:collapsed', !collapsed)"
      >
        <n-icon size="18" v-if="collapsed">
          <MenuUnfoldOutlined />
        </n-icon>
        <n-icon size="18" v-else>
          <MenuFoldOutlined />
        </n-icon>
      </div>
      <n-breadcrumb v-if="crumbsSetting.show">
        <template
          v-for="routeItem in breadcrumbList"
          :key="routeItem.name === 'Redirect' ? void 0 : routeItem.name"
        >
          <n-breadcrumb-item v-if="routeItem.meta.title">
            <span v-if="routeItem.children.length" @click="() => dropdownSelect(routeItem.name)">
              {{
                typeof routeItem.meta.title === 'function'
                  ? routeItem.meta.title(isCpUser)
                  : routeItem.meta.title
              }}
            </span>
            <span class="link-text" v-else>
              <component
                v-if="crumbsSetting.showIcon && routeItem.meta.icon"
                :is="routeItem.meta.icon"
              />
              {{
                typeof routeItem.meta.title === 'function'
                  ? routeItem.meta.title(isCpUser)
                  : routeItem.meta.title
              }}
            </span>
          </n-breadcrumb-item>
        </template>
      </n-breadcrumb>
    </div>
    <div class="layout-header-right">
      <div v-if="isCpUser">
        技术规范：
        <el-dropdown
          @command="selectProvider"
          class="align-middle"
          max-height="300px"
          :teleported="false"
        >
          <span class="el-dropdown-link">
            <span
              :title="currentProvider?.title"
              class="inline-block truncate"
              style="max-width: 10em"
            >
              {{ currentProvider?.title ?? '' }}</span
            >
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="p in providers" :key="p.id" :command="p.id">{{
                p.title
              }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-divider direction="vertical" />
      </div>
      <div class="px-2 text-label-text-color opacity-90">
        角色：
        {{ info?.roles[0]?.name }}
      </div>
      <el-divider direction="vertical" />
      {{ info.name }}
      <div class="layout-header-trigger layout-header-trigger-min">
        <n-dropdown trigger="hover" @select="avatarSelect" :options="avatarOptions">
          <div class="avatar">
            <el-avatar v-if="info.avatarPath" :size="40" :src="info.avatarUrl" />
            <n-avatar v-else round>
              {{ info.name?.slice(0, 6) }}
              <template #icon>
                <UserOutlined />
              </template>
            </n-avatar>
          </div>
        </n-dropdown>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { websiteConfig } from '@/config/website.config';
  import { useProjectSetting } from '@/hooks/setting/useProjectSetting';
  import { AsideMenu } from '@/layout/components/Menu';
  import { PERMISSION_KEYS, useUserStore } from '@/store/modules/user';
  import { TABS_ROUTES } from '@/store/mutation-types';
  import { ArrowDown } from '@element-plus/icons-vue';
  import { NDialogProvider } from 'naive-ui';
  import { storeToRefs } from 'pinia';
  import { computed, defineComponent, reactive, ref, toRefs, unref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { MenuFoldOutlined, MenuUnfoldOutlined, UserOutlined } from '@vicons/antd';
  import { ElMessageBox, ElMessage, ElDivider } from 'element-plus';
  import { usePermission } from '@/hooks/web/usePermission';

  export default defineComponent({
    name: 'PageHeader',
    components: {
      MenuFoldOutlined,
      MenuUnfoldOutlined,
      UserOutlined,
      NDialogProvider,
      AsideMenu,
      ArrowDown,
    },
    props: {
      collapsed: {
        type: Boolean,
      },
      inverted: {
        type: Boolean,
      },
    },
    setup(props) {
      const userStore = useUserStore();
      const { hasPermission } = usePermission();
      const { navMode, navTheme, headerSetting, menuSetting, crumbsSetting } = useProjectSetting();
      const { currentProviderId, providers, info, isCpUser } = storeToRefs(userStore);
      const currentProvider = computed(() => {
        return providers.value?.find(({ id }) => currentProviderId.value == id) ?? {};
      });
      const drawerSetting = ref();

      const state = reactive({
        isCpUser,
        fullscreenIcon: 'FullscreenOutlined',
        navMode,
        navTheme,
        headerSetting,
        crumbsSetting,
      });

      const selectProvider = async (id) => {
        await userStore.selectProvider(id);
        window.location.reload();
      };

      const getInverted = computed(() => {
        return ['light', 'header-dark'].includes(unref(navTheme))
          ? props.inverted
          : !props.inverted;
      });

      const mixMenu = computed(() => {
        return unref(menuSetting).mixMenu;
      });

      const getChangeStyle = computed(() => {
        const { collapsed } = props;
        const { minMenuWidth, menuWidth } = unref(menuSetting);
        return {
          left: collapsed ? `${minMenuWidth}px` : `${menuWidth}px`,
          width: `calc(100% - ${collapsed ? `${minMenuWidth}px` : `${menuWidth}px`})`,
        };
      });

      const getMenuLocation = computed(() => {
        return 'header';
      });

      const router = useRouter();
      const route = useRoute();

      const generator: any = (routerMap) => {
        return routerMap.map((item) => {
          const currentMenu = {
            ...item,
            label: item.meta.title,
            key: item.name,
            disabled: item.path === '/',
          };
          // 是否有子菜单，并递归处理
          if (item.children && item.children.length > 0) {
            // Recursion
            currentMenu.children = generator(item.children, currentMenu);
          }
          return currentMenu;
        });
      };

      const breadcrumbList = computed(() => {
        return generator(route.matched);
      });

      const dropdownSelect = (key) => {
        router.push({ name: key });
      };

      // 刷新页面
      const reloadPage = () => {
        router.push({
          path: '/redirect' + unref(route).fullPath,
        });
      };

      // 退出登录
      const doLogout = async () => {
        try {
          const action = await ElMessageBox({
            title: '确认退出登录',
            showClose: true,
            showCancelButton: true,
            message: '您确定要退出登录吗',
          });
          if (action == 'confirm') {
            await userStore.logout();
            ElMessage.success('成功退出登录');
            // 移除标签页
            localStorage.removeItem(TABS_ROUTES);
            router.replace({
              name: 'Login',
            });
          }
        } catch (e) {console.log(e)}
      };

      // 切换全屏图标
      const toggleFullscreenIcon = () =>
        (state.fullscreenIcon =
          document.fullscreenElement !== null ? 'FullscreenExitOutlined' : 'FullscreenOutlined');

      // 监听全屏切换事件
      document.addEventListener('fullscreenchange', toggleFullscreenIcon);

      // 全屏切换
      const toggleFullScreen = () => {
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen();
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          }
        }
      };

      const avatarOptions = [
/*         {
          label: '更改密码',
          key: 1,
        },
        {
          label: '修改手机号',
          key: 3,
        }, */
        {
          label: '退出登录',
          key: 2,
        },
      ];

      //头像下拉菜单
      const avatarSelect = (key) => {
        switch (key) {
          case 1:
            router.push({ name: 'ChangePwd' });
            break;
          case 2:
            doLogout();
            break;
          case 3:
            router.push({ name: 'ChangePhone' });
            break;
        }
      };

      function openSetting() {
        const { openDrawer } = drawerSetting.value;
        openDrawer();
      }

      return {
        ...toRefs(state),
        info,
        toggleFullScreen,
        doLogout,
        route,
        providers,
        currentProvider,
        dropdownSelect,
        avatarOptions,
        getChangeStyle,
        avatarSelect,
        breadcrumbList,
        selectProvider,
        reloadPage,
        drawerSetting,
        openSetting,
        getInverted,
        getMenuLocation,
        mixMenu,
        websiteConfig,
        hasPermission,
        PERMISSION_KEYS,
      };
    },
  });
</script>

<style lang="less" scoped>
  .layout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    height: 64px;
    box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
    transition: all 0.2s ease-in-out;
    width: 100%;
    z-index: 11;

    &-left {
      display: flex;
      align-items: center;

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 64px;
        line-height: 64px;
        overflow: hidden;
        white-space: nowrap;
        padding-left: 10px;

        img {
          width: auto;
          height: 32px;
          margin-right: 10px;
        }

        .title {
          margin-bottom: 0;
        }
      }

      :deep(.ant-breadcrumb span:last-child .link-text) {
        color: #515a6e;
      }

      .n-breadcrumb {
        display: inline-block;
      }

      &-menu {
        color: var(--text-color);
      }
    }

    &-right {
      display: flex;
      align-items: center;
      margin-right: 20px;

      .avatar {
        display: flex;
        align-items: center;
        height: 64px;
      }

      .el-dropdown {
        cursor: pointer;
      }
    }

    &-trigger {
      display: inline-block;
      width: 64px;
      height: 64px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease-in-out;

      .n-icon {
        display: flex;
        align-items: center;
        height: 64px;
        line-height: 64px;
      }

      &:hover {
        background: hsla(0, 0%, 100%, 0.08);
      }

      .anticon {
        font-size: 16px;
        color: #515a6e;
      }
    }

    &-trigger-min {
      width: auto;
      padding: 0 12px;
    }
  }

  .layout-header-light {
    background: #fff;
    color: #515a6e;

    .n-icon {
      color: #515a6e;
    }

    .layout-header-left {
      :deep(.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link) {
        color: #515a6e;
      }
    }

    .layout-header-trigger {
      &:hover {
        background: #f8f8f9;
      }
    }
  }

  .layout-header-fix {
    position: fixed;
    top: 0;
    right: 0;
    left: 200px;
    z-index: 11;
  }
</style>
