import { http } from '@/utils/http/axios';

/**
 * @description: 背景音乐列表
 */
export function getAssetBgmList(params: any = { page: 1, limit: 10, key: '' }) {
  return http.request({
    url: '/business/asset-bgm',
    method: 'GET',
    params,
  });
}

export function getGuestAssetBgmList(params: any = { page: 1, limit: 10, key: '' }) {
  return http.request({
    url: '/business/asset-bgm/audit/listGuest',
    method: 'GET',
    params,
  });
}

/**
 * @description: 新增背景音乐
 */
export function addAssetBgm(params = {}) {
  return http.request({
    url: '/business/asset-bgm',
    method: 'POST',
    params,
  });
}

/**
 * @description: 更新背景音乐
 */
export function updateAssetBgm(id, params = {}) {
  return http.request({
    url: `/business/asset-bgm/${id}`,
    method: 'POST',
    params,
  });
}

/**
 * @description: 获取背景音乐
 */
export function getAssetBgm(id, params = {}) {
  return http.request({
    url: `/business/asset-bgm/${id}`,
    method: 'GET',
    params,
  });
}

/**
 * @description: 删除背景音乐
 */
export function deleteAssetBgm(id) {
  return http.request({
    url: `/business/asset-bgm/${id}/delete`,
    method: 'POST',
  });
}

/**
 * @description: 删除背景音乐
 */
export function batchDeleteAssetBgm(params) {
  return http.request({
    url: `/business/asset-bgm/batch/deleteBatch`,
    method: 'POST',
    params,
  });
}

/**
 * @description: 背景音乐列表
 */
export function getAuditBgmList(params) {
  return http.request({
    url: '/business/asset-bgm/audit/list',
    method: 'GET',
    params,
  });
}

/**
 * @description: 发布背景音乐
 */
export function publishBgm(id) {
  return http.request({
    url: `/business/asset-bgm/${id}/publish`,
    method: 'POST',
  });
}

/**
 * @description: 撤回发布背景音乐
 */
export function revokeBgm(id) {
  return http.request({
    url: `/business/asset-bgm/${id}/revoke`,
    method: 'POST',
  });
}

/**
 * @description: 审核人员下架背景音乐
 */
export function revisingBgm(id) {
  return http.request({
    url: `/business/asset-bgm/${id}/revising`,
    method: 'POST',
  });
}

/**
 * @description: 审核背景音乐
 */
export function auditBgm(id, params) {
  return http.request({
    url: `/business/asset-bgm/${id}/audit`,
    method: 'POST',
    params,
  });
}
