<template>
  <el-card class="box-card">
    <div class="flex">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline flex flex-1">
        <el-form-item label="音乐状态" class="w-48">
          <el-select v-model="queryParams.status" clearable placeholder="请选择音乐状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              clearable
            />
          </el-select>
        </el-form-item>
        <el-form-item label="制作CP" class="w-48">
          <el-select clearable v-model="queryParams.user_id" placeholder="请选择制作CP">
            <el-option :value="''" :key="''" :label="'全部'" />
            <el-option
              v-for="item in userOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              clearable
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间：" class="w-80">
          <el-date-picker
            clearable
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            v-model="queryParams.dateRange"
            :disabled-date="disabledDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-input
            :prefix-icon="Search"
            v-model.trim="queryParams.keyword"
            placeholder="请输入音乐名称"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="default"
            @click="handleClearAll"
          >
            一键清除
          </el-button>
        </el-form-item>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column label="音乐名称" width="250" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex ml-2 items-center">
            <el-image
              preview-teleported
              class="w-16 h-16 basis-16"
              :src="row.url_bgm_icon"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :z-index="10000"
              :preview-src-list="[row.url_bgm_icon]"
              fit="contain"
            >
              <template #error>
                <div class="flex justify-center items-center w-full h-full text-2xl">
                  <el-icon class="text-2xl"><icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="truncate block pl-1 flex-1">{{ row.name }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="file_path" min-width="160" label="音乐路径">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-tooltip trigger="hover" placement="top">
              <template #content>
                <span v-text="row.file_path" class="cursor-pointer"></span>
              </template>
              <template #default>
                <span v-text="row.file_path" class="cursor-pointer block truncate"></span>
              </template>
            </el-tooltip>
            <el-icon class="ml-1" @click="handleCopy(row)">
              <DocumentCopy class="cursor-copy" />
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="provider"
        label="所属技术规范"
        width="160"
        :show-overflow-tooltip="true"
      >
        <template #default="{ row: { provider } }">
          <span>{{ provider.title }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="user.name" label="制作CP" width="160" :show-overflow-tooltip="true" />
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :min-width="item.minWidth"
          :formatter="item.formatter"
          show-overflow-tooltip
        />
      </template>
      <el-table-column label="操作" width="160">
        <template #default="{ row }">
          <el-button
            type="primary"
            :text="true"
            class="m-0 p-0 mr-2"
            @click="handleDetailMusic(row)"
            >详情</el-button
          >
          <el-button
            type="primary"
            class="m-0 p-0 mr-2"
            :text="true"
            @click="handleTryListen(row)"
            >{{ currentRowId == row.id && isPlaying ? '暂停' : '试听' }}</el-button
          >
          <el-button
            type="primary"
            :text="true"
            class="m-0 p-0 mr-2"
            v-if="EAssetStatus.Reviewing == row.status"
            @click="handleAuditMusic(row)"
            >审核</el-button
          >
          <el-button
            :text="true"
            class="m-0 p-0"
            v-if="[EAssetStatus.Published].includes(row.status)"
            type="danger"
            @click="handleWithdraw(row)"
            >下架</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="flex my-4 justify-end"
      background
      layout="total, prev, pager, next, sizes, jumper"
      v-model:page-size="queryParams.limit"
      v-model:current-page="queryParams.page"
      :total="totalRef"
    />

    <el-dialog v-model="auditDialogVisible" destroy-on-close :title="isAudit ? '背景音乐审核' : '背景音乐详情'" center>
      <el-form>
        <el-form-item label="音乐icon:">
          <el-image
            v-if="auditForm.urlBgmIcon"
            preview-teleported
            class="w-4/5 max-w-max h-auto max-h-48 preview-image"
            :src="auditForm.urlBgmIcon"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :z-index="10000"
            :preview-src-list="[auditForm.urlBgmIcon]"
            fit="contain"
          >
            <template #error>
              <div class="flex justify-center items-center w-full h-full text-2xl">
                <el-icon class="text-2xl"><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
        </el-form-item>
        <el-form-item label="音乐名称:">
          {{ auditForm.name }}
        </el-form-item>
        <el-form-item label="驳回原因:" v-if="auditForm.status === EAssetStatus.Rejected">
          {{ auditForm.revising_reason }}
        </el-form-item>
        <el-form-item label="音频备注:">
          {{ auditForm.remark }}
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="isAudit">
            <el-popconfirm width="200" title="确定审核通过吗?" @confirm="handleSubmitAudit(1)">
              <template #reference>
                <el-button>通过</el-button>
              </template>
            </el-popconfirm>
            <el-button type="danger" @click="innerAuditDialogVisible = true"> 驳回 </el-button>
          </template>
          <template v-else>
            <el-button @click="auditDialogVisible = false"> 返回 </el-button>
          </template>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="innerAuditDialogVisible" destroy-on-close title="审核意见">
      <el-form ref="auditFormRef" :model="auditForm" :rules="rules" status-icon>
        <el-form-item label="审核意见:" class="w-full" prop="audit_reason">
          <el-input
            placeholder="请描述"
            v-model="auditForm.audit_reason"
            rows="6"
            type="textarea"
            maxlength="100"
            @input="(value) => handleInput('audit_reason', value)"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="innerAuditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitOverrule">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script lang="ts" setup>
  import { auditBgm, getAuditBgmList, revisingBgm } from '@/api/bgm';
  import { dateTimeFormatter } from '@/utils/format-table-datetime';
  import useAudio from '@/views/hooks/useAudio';
  import { Search } from '@element-plus/icons-vue';
  import { useDebounceFn, useClipboard, useThrottleFn } from '@vueuse/core';
  import 'dayjs/locale/zh-cn';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { h, onMounted, reactive, onUnmounted, ref, watch } from 'vue';
  import { useExport, useAllCpUsers, useFilterInputHander } from '@/views/hooks';
  import { disabledDate } from '@/utils/dateUtil';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { DocumentCopy } from '@element-plus/icons-vue';
  import { Picture as IconPicture } from '@element-plus/icons-vue';
import { EAssetStatus } from '../materialManage/avatar/columns';

  const userStore = useUserStore();
  const { isCpUser } = storeToRefs(userStore);
  const { setSrc, isPlaying, pause } = useAudio();

  const currentRowId = ref('');
  const totalRef = ref(0);
  const auditDialogVisible = ref(false);
  const innerAuditDialogVisible = ref(false);
  const userOptions = useAllCpUsers(isCpUser.value);
  const auditFormRef = ref();
  const { exportFile } = useExport();

  const STATUS_MAP = {
    [EAssetStatus.Draft]: '草稿',
    [EAssetStatus.Reviewing]: '待审核',
    [EAssetStatus.Published]: '审核通过',
    [EAssetStatus.Rejected]: '驳回待修改',
    [EAssetStatus.Unshelve]: '下架待修改',
  };

  const STATUS_COLOR_MAP = {
    1: '#C4C4C4;',
    2: '#FAAD14;',
    3: '#06BF7F;',
    4: '#FF2020;',
    5: '#FF2020;',
  };

  const queryParams = reactive({
    keyword: '',
    status: '',
    user_id: '',
    limit: 10,
    dateRange: [] as any,
    page: 1,
  });

  const auditForm = reactive({
    id: '',
    name: '',
    status: 0,
    remark: '',
    revising_reason:'',
    urlBgmIcon: '',
    audit_reason: '',
  });
  const handleInput = useFilterInputHander(auditForm);

  const source = ref('');
  const { copy, isSupported } = useClipboard({ source });

  const handleCopy = useThrottleFn((row) => {
    source.value = row.file_path;
    if (!isSupported) {
      ElMessage.warning('您的浏览器不支持复制!');
      return;
    }
    copy(source.value);
    ElMessage.success('复制成功!');
    source.value = '';
  }, 3000);

  const handleSubmitOverrule = async () => {
    try {
      const valid = await auditFormRef.value?.validate();
      if (valid) {
        handleSubmitAudit(0);
      }
    } catch (e) {console.error(e)}
  };

  const rules = reactive({
    audit_reason: [
      {
        required: true,
        message: '请输入审批意见',
        trigger: ['blur'],
      },
    ],
  });

  watch(
    () => queryParams,
    async () => {
      await fetchBgmList();
    },
    { deep: true }
  );

  onUnmounted(() => {
    pause();
  });

  const statusOptions = [
    { label: '全部', value: '' },
    {
      label: '待审核',
      value: 2,
    },
    {
      label: '审核通过',
      value: 3,
    },
    {
      label: '驳回待修改',
      value: 4,
    },
    {
      label: '下架待修改',
      value: 5,
    },
  ];

  const tableData = ref([]);

  const handleTryListen = (row) => {
    if (isPlaying.value) {
      pause();
      if (row.id !== currentRowId.value) {
        setSrc(row.url_bgm, { autoPlay: true });
      }
    } else {
      setSrc(row.url_bgm, { autoPlay: true });
    }
    currentRowId.value = row.id;
  };

  const generateParamas = () => {
    const { dateRange, user_id, ...other } = queryParams;
    const params: any = { ...other };
    if (dateRange && Array.isArray(dateRange) && dateRange.length > 0) {
      const [start, end] = dateRange;
      params.create_start = start;
      params.create_end = end;
    }
    if (user_id) {
      params.user_id = user_id;
    }
    return params;
  };

  const fetchBgmList = useDebounceFn(async () => {
    const params = generateParamas();
    const {
      data: { total, rows: list },
    } = await getAuditBgmList(params);
    tableData.value = list;
    totalRef.value = total;
  }, 500);

  onMounted(async () => {
    await fetchBgmList();
  });

  const statusFormatter = (_row, _column, status, _index) => {
    return h('span', { style: `color: ${STATUS_COLOR_MAP[status]}` }, STATUS_MAP[status]);
  };

  const tableColumns = [
    { prop: 'created_at', label: '创建时间', show: true, width: 200, formatter: dateTimeFormatter },
    { prop: 'updated_at', label: '更新时间', show: true, width: 200, formatter: dateTimeFormatter },
    {
      prop: 'status',
      label: '音乐状态',
      show: true,
      formatter: statusFormatter,
      width: 160,
      minWidth: 150,
    },
  ];

  //导出
  const handleExport = () => {
    const params = generateParamas();
    exportFile('/business/export-query/bgm/export/audit/list', params, params);
  };
  const isAudit = ref(true)
  //审核
  const handleAuditMusic = async (row) => {
    isAudit.value = true
    auditDialogVisible.value = true;
    auditForm.name = row.name;
    auditForm.remark = row.remark;
    auditForm.revising_reason = row.revising_reason;
    auditForm.urlBgmIcon = row.url_bgm_icon;
    auditForm.id = row.id;
  };

  const handleDetailMusic = async (row) => {
    isAudit.value = false
    auditDialogVisible.value = true;
    auditForm.name = row.name;
    auditForm.remark = row.remark;
    auditForm.revising_reason = row.revising_reason;
    auditForm.urlBgmIcon = row.url_bgm_icon;
    auditForm.status = row.status;
    auditForm.id = row.id;
  }
  const handleSubmitAudit = async (audit_result) => {
    const params: { audit_reason?: string; audit_result: 0 | 1 } = { audit_result };
    if (audit_result === 0) {
      params.audit_reason = auditForm.audit_reason;
    }
    const { code } = await auditBgm(auditForm.id, params);
    if (code === 0) {
      auditForm.id = '';
      auditForm.name = '';
      auditForm.remark = '';
      auditForm.urlBgmIcon = '';
      auditForm.audit_reason = '';
      auditDialogVisible.value = false;
      innerAuditDialogVisible.value = false;
      await fetchBgmList();
    }
  };

  //撤回
  const handleWithdraw = async (row) => {
    try {
      const action = await ElMessageBox.confirm(`确认要下架背景音乐${row.name}吗?`, '确认下架', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      if (action == 'confirm') {
        const { code } = await revisingBgm(row.id);
        if (code === 0) {
          ElMessage.success('下架成功!');
          await fetchBgmList();
        }
      }
    } catch (e) {console.error(e)}
  };

  const handleClearAll = () => {
    // 重置所有搜索条件
    queryParams.keyword = '';
    queryParams.status = '';
    queryParams.user_id = '';
    queryParams.dateRange = [];
    queryParams.page = 1;

    ElMessage({
      type: 'success',
      message: '已清除所有搜索条件',
    });
  };
</script>

<style lang="less" scoped>
  .box-card {
    min-height: 800px;
    .img {
      width: 60px;
      height: 60px;
    }
  }
</style>
