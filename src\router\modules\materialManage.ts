import { Layout } from '@/router/constant';
import { renderIcon } from '@/utils/index';
import { PERMISSION_KEYS } from './audit';
import MaterialIcon from '@/components/menu-icons/cp/cpmaterial.vue';
import MaterialAIcon from '@/components/menu-icons/audit/material.vue';
/**
 * @param name 路由名称, 必须设置,且不能重名
 * @param meta 路由元信息（路由附带扩展信息）
 * @param redirect 重定向地址, 访问这个路由时,自定进行重定向
 * @param meta.disabled 禁用整个菜单
 * @param meta.title 菜单名称
 * @param meta.icon 菜单图标
 * @param meta.keepAlive 缓存该路由
 * @param meta.sort 排序越小越排前
 *
 * */
const routes: Array<any> = [
  {
    path: '/material',
    name: 'Material',
    redirect: '/material/material-list',
    component: Layout,
    meta: {
      /* title: (isCpUser) => (isCpUser ? '素材管理' : '素材审核'), */
      title: '素材管理',
      icon: (isCpUser) => (isCpUser ? renderIcon(MaterialIcon) : renderIcon(MaterialAIcon)),
      isRoot: true,
      sort: 1,
    },
    auth: [PERMISSION_KEYS.auditor.audit_user, PERMISSION_KEYS.cpuser.asset_read],
    children: [
      {
        path: 'material-list',
        name: 'material-list',
        meta: {
          /* title: '素材管理', */
          sort: 1,
        },
        component: () => import('@/views/materialManage/material/index.vue'),
      },
      {
        path: 'previewAvatar1',
        name: 'material-preview1',
        meta: {
          hidden: true,
          title: '素材预览',
          activeMenu: 'material-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
      {
        path: 'previewAvatar',
        name: 'material-preview',
        meta: {
          hidden: true,
          title: (isCpUser) => (isCpUser ? '素材预览' : '素材审核'),
          activeMenu: 'material-list',
        },
        component: () => import('@/views/materialManage/preview/index.vue'),
      },
      {
        path: 'model3d-preview',
        name: 'material-model3d-preview', // 修改为唯一的路由名称
        meta: {
          hidden: true,
          title: '3D模型预览',
          activeMenu: 'material-list',
        },
        component: () => import('@/components/Online3DViewer/Model3DPreview.vue'),
      },
      {
        path: 'materialAuditPreview',
        name: 'material-audit-preview-new',
        meta: {
          hidden: true,
          title: '素材审核',
          activeMenu: 'material-list',
        },
        component: () => import('@/views/materialManage/preview/MaterialAuditPreview.vue'),
      },
      {
        path: 'material-add',
        name: 'material-add',
        meta: {
          title: '新增素材',
          activeMenu: 'material-list',
          hidden: true,
        },
        component: () => import('@/views/materialManage/material/info.vue'),
      },
      {
        path: 'material-edit',
        name: 'material-edit',
        meta: {
          title: '编辑素材',
          activeMenu: 'material-list',
          hidden: true,
        },
        component: () => import('@/views/materialManage/material/info.vue'),
      },
      {
        path: '3d-model-demo',
        name: 'material-model-demo',
        meta: {
          title: '3D模型演示',
          hidden: true,
          activeMenu: 'material-list',
        },
        component: () => import('@/components/Online3DViewer/demo/DemoModel.vue'),
      },
    ],
  },
];

export default routes;
