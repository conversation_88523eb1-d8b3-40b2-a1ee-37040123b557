import Axios from 'axios'
import HmacSHA256 from 'crypto-js/hmac-sha256'
import { enc } from 'crypto-js'
import {
    axios,
} from "./axios"
import { http } from '@/utils/http/axios'

// 获取 token, 其他请求均依赖于 token
/* export async function getKey() {
    try {
        let res = await http.request({
            url: `/business/user/token/key`,
            method: 'GET',
        })
        console.log('res', res)
        if (res.code === 0) {
            return res.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
} */

// 公私域拆分
export async function getProjectV3(params) {
    try {
        let res = await axios({
            url: `/items/v3/app/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}

// 获取自定义资源快照
export async function getCentreConfigV3(params) {
    try {
        let res = await axios({
            url: `/items/v3/app/centre-config`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}

// 鉴权
export async function getAuthData(data) {
    let res = await axios({
        method: 'post',
        url: import.meta.env.VITE_AUTH_API_URL,
        data: { data },
        authorization: false,
        timeout: 5000,
    });
    if (res.data && res.data.code === 0 && res.data.data) {
        return res.data.data.cdata;
    } else if (res.status == 200 && res.data.code === 120005) {
        return;
    } else if (res.status == 200) {
        throw res.data.message;
    }
}

// (根据形象id)获取形象配置
export async function getAvatarInfo(params) {
    try {
        let res = await axios({
            url: `/items/avatar/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}
// (根据资源id)获取单个资源配置  =>  对应的批量接口 /items/resource/list
export async function getResourceById(params) {
    try {
        let res = await axios({
            url: `/items/v3/resource/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}

// 获取资源快照
export async function getItemsSnapshot(itemListUrl) {
    try {
        let res = await Axios.get(itemListUrl)
        if (res.status == 200) {
            return res.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}

// 公私域拆分
export async function getProjectV2(params) {
    try {
        let res = await axios({
            url: `/items/v2/project/query`,
            params,
            authorization: null,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data
        } else {
            throw res
        }
    } catch (err) { console.error(err) }
}


// 获取问题集
export async function getTalkSkills(params) {
    try {
        let res = await axios({
            url: `/demo/config/skill`,
            params,
            authorization: true,
        })
        if (res.status == 200 && res.data.code === 0) {
            return res.data.data;
        } else {
            throw res;
        }
    } catch (err) { console.error(err) }
}

// 动作列表接口
export async function getAnimationList(params) {
    let res = await axios({
        url: `/demo/config/animation`,
        params,
        authorization: true,
    })
    if (res.status == 200 && res.data.code === 0) {
        return res.data.data
    } else {
        throw res
    }
}
// 获取资源 arraybuffer
export const fetchArrayBufferByUrl = async function (url) {
    const res = await Axios.get(url, { responseType: "arraybuffer" })
    return res.data
}

// 获取资源文本
export const fetchTextByUrl = function (url) {
    return Axios.get(url, { responseType: "text" })
}