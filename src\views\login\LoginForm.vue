<template>
  <wrap-form title="模型资产管理平台" submitText="登录" @submit="debuncedSubmit" :showCancel="false">
    <el-form ref="formRef" label-placement="left" size="large" :model="formInline" :rules="rules">
      <el-form-item prop="phone">
        <el-input v-model="formInline.phone" maxlength="11" @input="(value) => handleInput('phone', value, 'number')"
          placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item prop="password">
        <el-input maxlength="16" @input="(value) => handleInput('password', value, 'no-zh-char')"
          v-model="formInline.password" type="password" :show-password="true" placeholder="请输入密码" />
      </el-form-item>
      <el-form-item prop="image_code">
        <div class="flex flex-1 items-center">
          <el-input class="flex-1" maxlength="4" @input="(value) => handleInput('image_code', value, 'number')"
            v-model="formInline.image_code" placeholder="请输入图形验证码" />
          <div class="w-28 ml-2" @click="handleGetCaptch">
            <img class="w-full" :src="captchaRef" alt="图片验证码" />
          </div>
        </div>
      </el-form-item>
      <el-form-item class="flex-1" prop="msg_code">
        <div class="flex flex-1 flex-between">
          <el-input @input="(value) => handleInput('msg_code', value, 'number')" class="flex-1"
            v-model="formInline.msg_code" maxlength="6" placeholder="请输入手机验证码" value="123456" readonly />
          <el-button disabled tertiary type="info"
            class="w-28 ml-2 bg-btn-bg-color border-none text-xx-blue" @click="handleGetMsgCode">
            {{ time > 0 ? `${time}s后获取` : '获取验证码' }}
          </el-button>
        </div>
      </el-form-item>
<!--       <el-form-item>
        <div class="flex flex-1 justify-end">
          <div class="justify-self-end">
            <a class="no-underline cursor-pointer text-xx-tip-color" @click="goToReset">忘记密码?</a>
          </div>
        </div>
      </el-form-item> -->
    </el-form>
    <!-- <template #tip>
      <p class="text-center">
        <a class="no-underline cursor-pointer text-xx-blue" @click="goToRegister">开发者注册</a>
      </p>
    </template> -->
  </wrap-form>
</template>

<script setup lang="ts">
import WrapForm from '@/components/wrap-form';
import { ResultEnum } from '@/enums/httpEnum';
import { PageEnum } from '@/enums/pageEnum';
import { useUserStore } from '@/store/modules/user';
import { useFilterInputHander, useEncrypt } from '@/views/hooks';
import useCountTime from '@/views/hooks/useCountTime';
import useMsgCode from '@/views/hooks/useMsgCode';
import { useDebounceFn, useThrottleFn, useTimeoutFn } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { reactive, onMounted, ref, toRaw, inject } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { validatePhone, validateImgCode, validateSmsCode } from '@/utils/validator';
import { ElMessage } from 'element-plus';
const initSdk: any = inject('initSdk');

const encrypt = useEncrypt();

const login = defineEmits(['login']);

const { start, time } = useCountTime();

const formRef = ref();
const LOGIN_NAME = PageEnum.BASE_LOGIN_NAME;

const formInline = reactive({
  phone: '',
  password: '',
  msg_code: '123456',
  image_code: '',
});

const handleInput = useFilterInputHander(formInline);

const rules = {
  phone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
  password: { required: true, message: '请输入密码', trigger: 'blur' },
  msg_code: [
    { required: true, message: '请输入手机验证码', trigger: 'blur' },
    { validator: validateSmsCode, trigger: 'blur' },
  ],
  image_code: [
    { required: true, message: '请输入图形验证码', trigger: 'blur' },
    { validator: validateImgCode, trigger: 'blur' },
  ],
};

const userStore = useUserStore();

const router = useRouter();
const route = useRoute();

const { captchaRef, fetchCaptch, getMsgCodeAction } = useMsgCode(formRef);

const handleGetMsgCode = async () => {
  try {
    const { phone, image_code } = formInline;
    formInline.msg_code = '';
    const sms_code = await getMsgCodeAction({ phone, image_code, type: 1 }, { auto: true });
    // 仅供开发测试环境下使用
    if (Number(sms_code) > 1000) {
      formInline.msg_code = sms_code + '';
    }
    start(60);
  } catch (e) {
    console.log('error', e)
  }
};

const handleGetCaptch = () => {
  formInline.image_code = '';
  fetchCaptch();
};

onMounted(() => {
  handleGetCaptch();
  localStorage.removeItem('ACCESS-TOKEN');
});

const debuncedSubmit = useDebounceFn(() => handleSubmit(), 1000);

const goToRegister = useThrottleFn(() => {
  router.push({ name: 'Register' });
}, 3000);

const goToReset = useThrottleFn(() => {
  router.push({ name: 'Reset' });
}, 3000);

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate();
    if (valid) {
      const originParams = toRaw(formInline);
      const params = encrypt(originParams);
      const { code, message: msg } = await userStore.login(params);
      await userStore.getInfo();
      const { isCpUser, isExpired } = storeToRefs(userStore);
      if (code == ResultEnum.SUCCESS) {
        initSdk();
        const toprop = decodeURIComponent((route.query?.redirect || '/') as string);
        if (isCpUser.value) {
          router.replace('/select-partner');
          return;
        } else {
          ElMessage({
            type: 'success',
            message: '登录成功，即将进入系统',
            onClose: () => {
              if (isExpired.value) {
                useTimeoutFn(
                  () =>
                    ElMessage({
                      type: 'warning',
                      message: '您的密码长时间未更新，为保证账户安全，请及时修改密码！',
                      duration: 3000,
                      showClose: true,
                    }),
                  1000
                );
              }
            },
          });
          if (route.name === LOGIN_NAME && !isCpUser.value) {
            router.replace('/welcome/page');
          } else {
            router.replace(toprop);
          }
        }
      } else {
        ElMessage.error(msg || '登录失败');
      }
    } else {
      ElMessage.error('请填写完整信息，并且进行验证码校验');
    }
  } catch (e: any) { }
};
</script>
